import messaging from '@react-native-firebase/messaging';
import {useEffect} from 'react';
// import { setFcmToken } from "../redux/slices/fcmTokenSlice";
import {PermissionsAndroid, Platform} from 'react-native';
import Toast from 'react-native-toast-message';
import {useAppDispatch, useAppSelector} from '../store';
import {NavigationContainerRef} from '@react-navigation/native';

let navigationRef: NavigationContainerRef<any> | null = null;

export const setNavigationRef = (ref: NavigationContainerRef<any>) => {
  navigationRef = ref;
};

export const DeeplinkHandlerHOC = () => {
  const MessageHandlerComponent = () => {
    // const dispatch = useAppDispatch();
    const user = useAppSelector(state => state.user.user);

    const getFCMToken = async () => {
      try {
        const fcmToken = await messaging().getToken();
        if (fcmToken) {
          // setFcmToken(fcmToken);
        }
      } catch (error) {
        console.log('Error in fetching FCM token:', error);
      }
    };

    const notificationListener = async () => {
      messaging().onNotificationOpenedApp(remoteMessage => {
        const parseData = remoteMessage.data;
        const screen = parseData?.screen;
        const recipientId = parseData?.recipientId;

        if (screen === 'ChatDetails') {
          navigationRef?.navigate(screen, {recipientId}); // ChatDetails, etc.
        } else {
          console.log(' Screen not found');
        }
        if (remoteMessage) {
          console.log('Remote Notification Listener>>>', remoteMessage.data);
        }
      });

      messaging().setBackgroundMessageHandler(async remoteMessage => {
        console.log('Remote message', remoteMessage.data);
        const parseData = remoteMessage.data;
        const screen = parseData?.screen;
        const recipientId = parseData?.recipientId;

        // if (screen === "ChatDetails") {
        //   NavigationService.navigate(screen, { recipientId }); // ChatDetails, etc.
        // } else {
        //   console.log("hello world");

        // }
      });

      messaging()
        .getInitialNotification()
        .then(remoteMessage => {
          if (remoteMessage) {
            console.log(
              'Notification caused app to open from quit state:',
              remoteMessage.notification,
            );
          }
        });

      messaging().onMessage(async remoteMessage => {
        console.log('Notification received in foreground:', remoteMessage.data);
      });
    };

    const requestAndroidNotificationPermission = async () => {
      if (Platform.OS === 'android') {
        const granted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.POST_NOTIFICATIONS,
        );

        if (granted === PermissionsAndroid.RESULTS.GRANTED) {
          console.log('Notification permission granted.');
          return true;
        } else {
          console.log('Notification permission denied.');
          return false;
        }
      }
      return true;
    };

    const requestUserPermission = async () => {
      if (Platform.OS === 'android') {
        const hasPermission = await requestAndroidNotificationPermission();
        if (!hasPermission) return;
      }

      const authStatus = await messaging().requestPermission();
      const enabled =
        authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
        authStatus === messaging.AuthorizationStatus.PROVISIONAL;

      if (enabled) {
        console.log('FCM permission granted');
        getFCMToken();
      } else {
        console.log('FCM permission denied');
      }
    };

    useEffect(() => {
      requestUserPermission();
      notificationListener();
    }, []);

    return;
  };
  return MessageHandlerComponent;
};
